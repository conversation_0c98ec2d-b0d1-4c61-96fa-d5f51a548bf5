import { Instagram, Linkedin, Twitter, Facebook, Mail, Phone, MapPin } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="text-2xl font-bold mb-4">
              <span className="text-coral">Skyline</span> Digital Solution
            </div>
            <p className="text-slate-300 mb-6 max-w-md">
              We&apos;re not your standard agency. Skyline Digital Solution builds brands that dominate
              with sharp strategy and wild creativity.
            </p>
            <div className="flex space-x-4">
              <Instagram className="h-6 w-6 text-slate-400 hover:text-coral cursor-pointer transition-colors" />
              <Linkedin className="h-6 w-6 text-slate-400 hover:text-coral cursor-pointer transition-colors" />
              <Twitter className="h-6 w-6 text-slate-400 hover:text-coral cursor-pointer transition-colors" />
              <Facebook className="h-6 w-6 text-slate-400 hover:text-coral cursor-pointer transition-colors" />
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#home" className="text-slate-300 hover:text-coral transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="#services" className="text-slate-300 hover:text-coral transition-colors">
                  Services
                </a>
              </li>
              <li>
                <a href="#about" className="text-slate-300 hover:text-coral transition-colors">
                  About
                </a>
              </li>
              <li>
                <a href="#contact" className="text-slate-300 hover:text-coral transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="h-5 w-5 text-coral mr-3" />
                <span className="text-slate-300"><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="h-5 w-5 text-coral mr-3" />
                <span className="text-slate-300">+****************</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-coral mr-3" />
                <span className="text-slate-300">New York, NY</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-700 mt-8 pt-8 text-center">
          <p className="text-slate-400">
            © 2024 Skyline Digital Solution. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
