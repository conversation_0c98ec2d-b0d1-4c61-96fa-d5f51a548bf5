"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";

const HeroSection = () => {
  return (
    <section className="relative bg-gradient-to-b from-slate-50 to-slate-100 overflow-hidden">

      {/* Content */}
      <div className="relative z-10 container mx-auto px-6 py-20">
        <div className="max-w-4xl">
          {/* Left Column - Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-slate-800 space-y-8"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              <div className="text-6xl md:text-7xl lg:text-8xl font-bold leading-tight text-slate-700">
                WE
              </div>
              
              <h1 className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight text-slate-800">
                Build Your<br />
                Career & Business<br />
                <span className="text-red-500">Expert</span> <span className="text-slate-800">Services</span>
              </h1>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="space-y-4 max-w-3xl"
            >
              <p className="text-lg md:text-xl text-slate-600 leading-relaxed">
                We&apos;re not your standard agency — and we never will be. <strong className="text-red-500 font-semibold">Skyline Digital Solution</strong> was
                built for the bold: brands, creators, and dreamers who crave more than safe ideas and
                recycled strategies.
              </p>
              <p className="text-lg text-slate-600 leading-relaxed">
                Our team fuses sharp strategy with wild creativity to build brands that not only exist, but
                also dominate. We believe in risk, rebellion, and relentless results.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                size="lg"
                className="px-8 py-4 text-lg font-semibold group hover:scale-105 transition-all duration-300 bg-red-500 hover:bg-red-600 text-white rounded-lg shadow-lg hover:shadow-xl"
              >
                Read More
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;