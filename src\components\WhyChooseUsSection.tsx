"use client";

import { Clock, TrendingUp, Target, DollarSign } from "lucide-react";
import { motion } from "framer-motion";

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: Clock,
      title: "Save Time, Scale Faster",
      description: "Efficient workflows and fast turnarounds help you launch projects quickly and stay ahead of the competition."
    },
    {
      icon: DollarSign,
      title: "Invest Smart, Get More",
      description: "High-value services tailored to your goals, giving you premium results that fit your budget.",
      highlight: true
    },
    {
      icon: Target,
      title: "Proven Results",
      description: "Our data-driven approach delivers measurable outcomes that drive your business forward."
    }
  ];

  return (
    <section className="bg-slate-800 py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            Why Choose <span className="text-coral">Us</span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Your strategic partner for visionary solutions, proven expertise, and measurable impact.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`text-center p-8 rounded-lg ${feature.highlight ? 'bg-coral' : 'bg-slate-700'}`}
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 ${feature.highlight ? 'bg-white/20' : 'bg-coral'} rounded-lg mb-6`}>
                <feature.icon className={`h-8 w-8 ${feature.highlight ? 'text-white' : 'text-white'}`} />
              </div>

              <h3 className={`text-xl font-bold mb-4 ${feature.highlight ? 'text-white' : 'text-white'}`}>
                {feature.title}
              </h3>

              <p className={`leading-relaxed ${feature.highlight ? 'text-white/90' : 'text-slate-300'}`}>
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUsSection;