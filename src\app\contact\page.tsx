import { Metada<PERSON> } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Contact Us - Skyline Digital Solution',
  description: 'Get in touch with Skyline Digital Solution for expert digital marketing services. We&apos;re here to help transform your business.',
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-primary text-primary-foreground py-16">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold mb-4">
            Contact <span className="text-accent">Us</span>
          </h1>
          <p className="text-xl text-primary-foreground/80 max-w-2xl mx-auto">
            Ready to transform your business? Let&apos;s start the conversation.
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card className="text-center">
              <CardHeader>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-accent rounded-lg mb-4 mx-auto">
                  <Mail className="h-6 w-6 text-accent-foreground" />
                </div>
                <CardTitle className="text-lg">Email</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription><EMAIL></CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-coral rounded-lg mb-4 mx-auto">
                  <Phone className="h-6 w-6 text-coral-foreground" />
                </div>
                <CardTitle className="text-lg">Phone</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>+****************</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-accent rounded-lg mb-4 mx-auto">
                  <MapPin className="h-6 w-6 text-accent-foreground" />
                </div>
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>New York, NY</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-coral rounded-lg mb-4 mx-auto">
                  <Clock className="h-6 w-6 text-coral-foreground" />
                </div>
                <CardTitle className="text-lg">Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>Mon-Fri: 9AM-6PM</CardDescription>
              </CardContent>
            </Card>
          </div>

          {/* CTA Section */}
          <div className="text-center bg-section-bg rounded-lg p-12">
            <h2 className="text-3xl font-bold text-primary mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Book a free 15-minute consultation to discuss your project and see how we can help transform your business.
            </p>
            <Button variant="coral" size="lg" className="px-8 py-6 text-lg font-semibold">
              Book Free Consultation
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
