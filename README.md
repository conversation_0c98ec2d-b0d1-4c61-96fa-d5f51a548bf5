# Skyline Digital Solution - Marketing Website

A modern, responsive marketing website built with Next.js 15.3.3, TypeScript, and Tailwind CSS. This project showcases digital marketing services with a professional design and optimal performance.

## 🚀 Features

- **Next.js 15.3.3** with App Router
- **TypeScript** for type safety
- **Tailwind CSS v4** for styling
- **Radix UI** components for accessibility
- **Lucide React** icons
- **Responsive design** for all devices
- **SEO optimized** with proper metadata
- **Performance optimized** with Turbopack

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and CSS variables
│   ├── layout.tsx         # Root layout with metadata
│   ├── page.tsx           # Home page
│   ├── loading.tsx        # Loading UI
│   ├── error.tsx          # Error UI
│   ├── not-found.tsx      # 404 page
│   └── hooks/             # Custom React hooks
│       ├── use-mobile.tsx
│       └── use-toast.ts
├── components/            # React components
│   ├── Header.tsx         # Navigation header
│   ├── HeroSection.tsx    # Hero section
│   ├── WhyChooseUsSection.tsx
│   ├── ServicesSection.tsx
│   ├── AchievementsSection.tsx
│   ├── TeamSection.tsx
│   ├── Footer.tsx         # Footer component
│   └── ui/               # Reusable UI components
│       ├── button.tsx
│       ├── card.tsx
│       ├── avatar.tsx
│       └── ... (other UI components)
└── lib/
    └── utils.ts          # Utility functions
```

## 🛠 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd d_marketing_amjad
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📦 Dependencies

### Core Dependencies
- **next**: 15.5.3
- **react**: 19.1.0
- **react-dom**: 19.1.0

### UI & Styling
- **tailwindcss**: ^4
- **@tailwindcss/postcss**: ^4
- **lucide-react**: Icons
- **@radix-ui/react-***: Accessible UI primitives
- **class-variance-authority**: Component variants
- **clsx**: Conditional classes
- **tailwind-merge**: Merge Tailwind classes

## 🎨 Design System

The project uses a comprehensive design system defined in `globals.css`:

- **Colors**: HSL-based color system with light/dark mode support
- **Typography**: Geist Sans and Geist Mono fonts
- **Spacing**: Consistent spacing scale
- **Components**: Reusable UI components with variants

## 📱 Responsive Design

The website is fully responsive with:
- Mobile-first approach
- Breakpoints: sm, md, lg, xl
- Flexible grid layouts
- Optimized images and assets

## 🔧 Scripts

```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production with Turbopack
npm start            # Start production server
npm run lint         # Run ESLint
```

## 🚀 Deployment

The project is optimized for deployment on Vercel:

1. Push to GitHub
2. Connect to Vercel
3. Deploy automatically

For other platforms, run `npm run build` and deploy the `.next` folder.

## 📄 License

This project is private and proprietary to Skyline Digital Solution.
