"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, Award, Users, TrendingUp, Star } from "lucide-react";

const AchievementsSection = () => {
  const stats = [
    {
      number: "340+",
      label: "Projects Delivered",
      icon: Award,
      color: "from-blue-500 to-blue-600"
    },
    {
      number: "325+",
      label: "Satisfied Clients",
      icon: Users,
      color: "from-green-500 to-green-600"
    },
    {
      number: "95%",
      label: "Client Retention",
      icon: TrendingUp,
      color: "from-purple-500 to-purple-600"
    },
    {
      number: "7+",
      label: "Team Experts",
      icon: Star,
      color: "from-orange-500 to-orange-600"
    }
  ];

  return (
    <section className="bg-white py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Our <span className="text-coral">Achievements</span>
          </h2>
          <p className="text-xl text-slate-600 font-medium mb-8">
            Milestones That Define Our Impact
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="bg-slate-50 rounded-lg p-6 border border-slate-200">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-coral rounded-lg mb-4">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>

                <div className="text-3xl lg:text-4xl font-bold text-coral mb-2">
                  {stat.number}
                </div>

                <div className="text-sm font-medium text-slate-600">
                  {stat.label}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Description */}
        <div className="text-center max-w-4xl mx-auto mb-12">
          <p className="text-lg text-slate-600 leading-relaxed">
            At Skyline Digital Solution, every milestone marks a story of innovation, growth, and client success.
            From launching standout brands to driving measurable digital results, our achievements reflect our
            commitment to transforming businesses worldwide.
          </p>
        </div>

        <div className="text-center">
          <Button
            size="lg"
            className="bg-coral hover:bg-coral/90 text-white px-8 py-3 text-lg font-medium rounded-lg"
          >
            Learn More About Us
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default AchievementsSection;