"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { motion } from "framer-motion";
import { Linkedin, Twitter, Github, Mail } from "lucide-react";
import { useState } from "react";

const TeamSection = () => {
  const [hoveredMember, setHoveredMember] = useState<number | null>(null);

  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Lead Developer & Founder",
      description: "Full-stack architect with 8+ years of experience building scalable web applications and leading development teams.",
      avatar: "/images/team-member-1.jpg",
      skills: ["React", "Node.js", "AWS", "Leadership"],
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#",
        email: "<EMAIL>"
      }
    },
    {
      name: "<PERSON>",
      role: "UI/UX Designer",
      description: "Creative designer passionate about crafting beautiful, user-centered experiences that drive engagement and conversions.",
      avatar: "/images/team-member-2.jpg",
      skills: ["Figma", "Adobe Creative", "User Research", "Prototyping"],
      social: {
        linkedin: "#",
        twitter: "#",
        email: "<EMAIL>"
      }
    },
    {
      name: "Michael Rodriguez",
      role: "Digital Marketing Strategist",
      description: "Data-driven marketer specializing in SEO, PPC, and conversion optimization with proven track record of ROI growth.",
      avatar: "/images/team-member-3.jpg",
      skills: ["SEO", "Google Ads", "Analytics", "Content Strategy"],
      social: {
        linkedin: "#",
        twitter: "#",
        email: "<EMAIL>"
      }
    }
  ];

  return (
    <section className="bg-slate-50 py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Meet Our <span className="text-coral">Expert Team</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            The creative minds and technical experts turning your ideas into extraordinary digital results.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="bg-white text-center hover:shadow-lg transition-all duration-300 border border-slate-200 overflow-hidden h-full">
                <CardHeader className="pb-4">
                  <div className="flex justify-center mb-6">
                    <Avatar className="w-24 h-24 border-2 border-slate-200">
                      <AvatarImage src={member.avatar} alt={member.name} className="object-cover" />
                      <AvatarFallback className="bg-coral text-white text-xl font-bold">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  <CardTitle className="text-xl font-bold text-slate-800 mb-2">
                    {member.name}
                  </CardTitle>
                  <CardDescription className="text-coral font-semibold">
                    {member.role}
                  </CardDescription>
                </CardHeader>

                <CardContent className="px-6 pb-6">
                  <p className="text-slate-600 leading-relaxed mb-6">
                    {member.description}
                  </p>

                  {/* Skills */}
                  <div className="flex flex-wrap gap-2 justify-center mb-4">
                    {member.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-slate-100 text-slate-700 text-xs font-medium rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  {/* Social Links */}
                  <div className="flex justify-center space-x-3">
                    {member.social.linkedin && (
                      <a href={member.social.linkedin} className="p-2 bg-slate-100 rounded-full hover:bg-coral hover:text-white transition-colors">
                        <Linkedin className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.twitter && (
                      <a href={member.social.twitter} className="p-2 bg-slate-100 rounded-full hover:bg-coral hover:text-white transition-colors">
                        <Twitter className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.github && (
                      <a href={member.social.github} className="p-2 bg-slate-100 rounded-full hover:bg-coral hover:text-white transition-colors">
                        <Github className="h-4 w-4" />
                      </a>
                    )}
                    <a href={`mailto:${member.social.email}`} className="p-2 bg-slate-100 rounded-full hover:bg-coral hover:text-white transition-colors">
                      <Mail className="h-4 w-4" />
                    </a>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamSection;