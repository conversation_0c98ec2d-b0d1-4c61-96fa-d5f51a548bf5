"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Globe, ShoppingCart, Share2, BarChart3, Briefcase } from "lucide-react";
import { motion } from "framer-motion";
import { useState } from "react";

const ServicesSection = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  const services = [
    {
      icon: User,
      title: "Career Branding",
      description: "From entry-level to executive, Skyline Digital Solution builds personal brands that open doors. We position you for success.",
      image: "/images/service-marketing.jpg",
      features: [
        "Executive Resume/CV Development",
        "Tailored Cover Letter",
        "ATS Resume",
        "LinkedIn Profile Optimization"
      ],
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Globe,
      title: "WordPress Development",
      description: "We build sleek, lightning-fast WordPress sites templates. No fluff. Just performance.",
      image: "/images/service-web-dev.jpg",
      features: [
        "Custom Design & Development",
        "Landing Pages",
        "Website Redesign",
        "Maintenance & Support"
      ],
      color: "from-green-500 to-green-600"
    },
    {
      icon: ShoppingCart,
      title: "Shopify Development",
      description: "Skyline Digital Solution designs custom Shopify stores that make people click, buy, and come back. Seamless UX, bold visuals, and revenue-driven design.",
      image: "/images/service-ecommerce.jpg",
      features: [
        "Custom Store Setup",
        "Product Page Design",
        "Payment Integration",
        "Store Optimization"
      ],
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: Share2,
      title: "Social Media Management",
      description: "We combine creative energy with data-driven strategy to get your brand seen, clicked, and converting. No fluff. Just results.",
      image: "/images/service-marketing.jpg",
      features: [
        "Content Creation & Strategy",
        "Community Management",
        "Growth Campaigns",
        "Analytics & Reporting"
      ],
      color: "from-pink-500 to-pink-600"
    },
    {
      icon: BarChart3,
      title: "Digital Marketing",
      description: "We combine creative energy with data-driven strategy to get your brand seen, clicked, and converting. No fluff. Just results.",
      image: "/images/service-marketing.jpg",
      features: [
        "SEO & SEM Optimization",
        "Paid Advertising Campaigns",
        "Email Marketing Automation",
        "Conversion Rate Optimization"
      ],
      color: "from-orange-500 to-orange-600"
    },
    {
      icon: Briefcase,
      title: "Business Consulting",
      description: "Strategic guidance to transform your business vision into reality. We help you navigate challenges and unlock growth opportunities.",
      image: "/images/service-web-dev.jpg",
      features: [
        "Business Strategy Development",
        "Market Analysis & Research",
        "Process Optimization",
        "Growth Planning"
      ],
      color: "from-teal-500 to-teal-600"
    }
  ];

  return (
    <section className="bg-slate-50 py-20">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Our Services & <span className="text-coral">Features</span>
          </h2>
          <p className="text-xl text-coral font-semibold mb-6">
            Designed to Impress. Built to Perform.
          </p>
          <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Skyline Digital Solution transforms ideas into digital powerhouses — from personal branding and design to full-scale
            web development, e-commerce, and marketing strategies built to make your brand impossible to ignore.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="bg-white hover:shadow-lg transition-all duration-300 border border-slate-200 overflow-hidden group h-full">
                {/* Service Icon */}
                <div className="p-6 pb-4">
                  <div className="w-16 h-16 bg-coral/10 rounded-lg flex items-center justify-center mb-4">
                    <service.icon className="h-8 w-8 text-coral" />
                  </div>
                  
                  <CardTitle className="text-xl font-bold text-slate-800 mb-3">
                    {service.title}
                  </CardTitle>
                  <CardDescription className="text-slate-600 leading-relaxed">
                    {service.description}
                  </CardDescription>
                </div>

                <CardContent className="px-6 pb-6">
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center text-sm text-slate-600"
                      >
                        <div className="w-1.5 h-1.5 bg-coral rounded-full mr-3 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;