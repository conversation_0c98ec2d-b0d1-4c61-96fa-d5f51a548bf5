{"css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"html": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascriptreact", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "files.associations": {"*.css": "tailwindcss"}}