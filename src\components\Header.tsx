import { But<PERSON> } from "@/components/ui/button";
import { Calendar, Instagram, Linkedin, Twitter, Facebook } from "lucide-react";

const Header = () => {
  return (
    <header className="bg-slate-900 text-white">
      {/* Top banner */}
      <div className="bg-slate-800 py-2">
        <div className="container mx-auto px-6 flex items-center justify-between">
          <span className="text-sm text-slate-300">Get 10% off all services this month.</span>
          <div className="flex items-center space-x-2 text-sm text-slate-300">
            <Calendar className="h-4 w-4" />
            <span>Book free 15-min chat</span>
          </div>
        </div>
      </div>
      
      {/* Social media bar */}
      <div className="bg-slate-700 py-1">
        <div className="container mx-auto px-6 flex items-center justify-end space-x-4">
          <Instagram className="h-4 w-4 text-slate-400 hover:text-white cursor-pointer transition-colors" />
          <Linkedin className="h-4 w-4 text-slate-400 hover:text-white cursor-pointer transition-colors" />
          <Twitter className="h-4 w-4 text-slate-400 hover:text-white cursor-pointer transition-colors" />
          <Facebook className="h-4 w-4 text-slate-400 hover:text-white cursor-pointer transition-colors" />
        </div>
      </div>
      
      {/* Main header */}
      <div className="container mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="text-2xl font-bold text-white">
            <span className="text-coral">Skyline</span> DIGITAL SOLUTIONS
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-white hover:text-coral transition-colors font-medium">
              HOME
            </a>
            <a href="#services" className="text-white hover:text-coral transition-colors font-medium">
              SERVICES
            </a>
            <a href="#about" className="text-white hover:text-coral transition-colors font-medium">
              ABOUT
            </a>
            <a href="#contact" className="text-white hover:text-coral transition-colors font-medium">
              CONTACT
            </a>
          </nav>

          {/* CTA Button */}
          <Button 
            size="sm" 
            className="bg-coral hover:bg-coral/90 text-white px-6 py-2 rounded-md font-medium"
          >
            Get Started
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;